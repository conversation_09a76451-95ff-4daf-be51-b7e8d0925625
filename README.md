# Stabizard 🎥✨

A command-line tool for automatically finding and creating perfect loops from video files. St<PERSON><PERSON> analyzes your video to find the best loop points where the frames are most similar, creating seamless, infinitely looping videos. It can also optionally stabilize the resulting loop to reduce camera shake.

## Features

- Automatically detects the best loop points in a video
- Creates smooth transitions between loop points
- Optional video stabilization for smoother loops
- Supports multiple loop candidates
- Configurable minimum loop length and similarity threshold
- Progress bars for long operations
- Detailed output about found loop candidates

## Prerequisites

- Python 3.x
- OpenCV
- NumPy
- Click
- ffmpeg (for video stabilization)
- ffprobe-python

## Installation

1. Ensure you have Python 3.x installed

2. Install ffmpeg:
   - **macOS**: Install with Homebrew:
     ```bash
     brew install ffmpeg
     ```
   - **Linux**:
     - Ubuntu/Debian:
       ```bash
       sudo apt-get update
       sudo apt-get install ffmpeg
       ```
     - Fedora:
       ```bash
       sudo dnf install ffmpeg
       ```
     - Arch Linux:
       ```bash
       sudo pacman -S ffmpeg
       ```
   - **Windows**: 
     - Install using Chocolatey:
       ```bash
       choco install ffmpeg
       ```
     - Or download directly from [ffmpeg.org](https://ffmpeg.org/download.html#build-windows)
       and add it to your system PATH

3. Create and activate a virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Unix/macOS
   # or
   venv\Scripts\activate  # On Windows
   ```

4. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

After finding a loop, the tool will ask if you want to stabilize it (defaults to Yes):
- The original loop is first saved as is
- If stabilization is chosen:
  - The stabilized version will be saved as `{name}_stabilized.mp4`
  - Temporary files from the stabilization process are automatically cleaned up
- If stabilization fails, any temporary files are cleaned up and the original loop remains intact

## Usage

On Unix-like systems (macOS, Linux), you can run the scripts directly:
```bash
./stabizard.py -i myvideo.mp4 -o loop.mp4
./stabVid.py input.mp4
```

On Windows, use python to run the scripts:
```bash
python stabizard.py -i myvideo.mp4 -o loop.mp4
python stabVid.py input.mp4
```

### Using stabizard.py (Main Tool)

Find a loop with default settings:
```bash
./stabizard.py -i myvideo.mp4 -o loop.mp4
```

Create a longer loop with more lenient matching:
```bash
./stabizard.py -i myvideo.mp4 -o loop.mp4 --min-length 48 --threshold 0.8
```

Use the second-best loop candidate:
```bash
./stabizard.py -i myvideo.mp4 -o loop.mp4 --candidate 1
```

### Using stabVid.py (Standalone Stabilization)

You can also use the video stabilization component independently without loop detection:

```bash
./stabVid.py path/to/your/video.mp4
```

This will:
- Create a stabilized version of your video
- Save it as 'stabilized.mp4' in the same directory as your input video
- Use default stabilization settings (1.15x scale factor, -15 zoom factor)

Options:
- `--max-duration SECONDS`: Set maximum video duration in seconds (default: no limit)
- `--scale FACTOR`: Set video scale factor (must be positive, default: 1.15)
- `--zoom FACTOR`: Set video zoom factor (default: -15)

Examples:
```bash
# Process a long video
./stabVid.py --max-duration 600 path/to/your/video.mp4

# Adjust stabilization parameters
./stabVid.py --scale 1.2 --zoom -10 path/to/your/video.mp4

# Combine options
./stabVid.py --max-duration 300 --scale 1.3 --zoom -20 path/to/your/video.mp4
```

The scale factor determines how much the video is scaled up before stabilization (higher values allow more movement but may reduce quality). The zoom factor controls how much the final video is zoomed to hide black borders (more negative values = more zoom).

Parameter validation:
- Scale factor must be a positive number (e.g., 1.15, 1.3)
- Zoom factor must be a valid number (typically negative, e.g., -15, -20)
- Input file must exist and be readable
- Video duration must be between 1 second and the specified max duration

If any parameters are invalid, the tool will provide a helpful error message explaining the issue.

## How It Works

Stabizard uses a combination of Mean Squared Error (MSE) and Structural Similarity (SSIM) to find frames that are visually similar. It then:

1. Analyzes all frames in the video
2. Creates a similarity matrix comparing each frame with every other frame
3. Finds potential loop points where frames are most similar
4. Creates a smooth transition between the loop points
5. Optionally stabilizes the loop using ffmpeg's vidstab filter
6. Outputs the looped video segment(s)

The tool will show you multiple loop candidates with their scores, allowing you to choose the best one for your needs.

## Tips

- Shorter videos work better and process faster
- Videos with natural repetition (like a spinning object) work best
- If no good loops are found, try adjusting the threshold value
- Use the `--candidate` option to try different loop points if the first one isn't perfect
- Try stabilization if your video has camera shake or unwanted motion

## License

MIT License