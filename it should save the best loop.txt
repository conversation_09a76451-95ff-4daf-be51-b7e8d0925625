PS D:\projects\stabizard> venv\Scripts\activate
(venv) PS D:\projects\stabizard> python ./stabizard.py -i input.mp4 -o loop.mp4 --min-length 180 --threshold 0.99
Processing video: input.mp4
FPS: 59.94005994005994, Total frames: 751
Resolution: 2160x3840
Reading frames  [####################################]  100%
Analyzing frames  [####################################]  100%

Found 5 potential loops:
Candidate 0: frames 354 to 668 (length: 314 frames, 5.24 seconds, score: 0.855)
Candidate 1: frames 330 to 637 (length: 307 frames, 5.12 seconds, score: 0.859)
Candidate 2: frames 331 to 637 (length: 306 frames, 5.11 seconds, score: 0.892)
Candidate 3: frames 350 to 668 (length: 318 frames, 5.31 seconds, score: 0.894)
Candidate 4: frames 434 to 639 (length: 205 frames, 3.42 seconds, score: 0.905)

Using loop candidate 0: frames 354 to 668
Loop length: 314 frames (5.24 seconds)
Loop score: 0.855 (lower is better)
Saving original loop to: loop.mp4
Do you want to stabilize the loop? [Y/n]: y
Stabilizing video...
Stabilization complete
Stabilized loop saved to: loop_stabilized.mp4
Processing complete