(venv) PS D:\projects\stabizard> python ./stabizard.py -i input.mp4 -o loop.mp4 --min-length 60 --threshold 0.99
Processing video: input.mp4
FPS: 59.94005994005994, Total frames: 751
Resolution: 2160x3840
Reading frames  [####################################]  100%
Analyzing frames  [####################################]  100%

Found 5 potential loops:
Candidate 0: frames 464 to 611 (length: 147 frames, 2.45 seconds, score: 0.772)
Candidate 1: frames 464 to 610 (length: 146 frames, 2.44 seconds, score: 0.772)
Candidate 2: frames 465 to 611 (length: 146 frames, 2.44 seconds, score: 0.818)
Candidate 3: frames 465 to 610 (length: 145 frames, 2.42 seconds, score: 0.818)
Candidate 4: frames 354 to 668 (length: 314 frames, 5.24 seconds, score: 0.855)

Using loop candidate 0: frames 464 to 611
Loop length: 147 frames (2.45 seconds)
Loop score: 0.772 (lower is better)
Do you want to stabilize the loop? [y/N]: y
Stabilizing video...
cpe.returncode 3752568763
cpe.cmd ['ffmpeg', '-y', '-i', 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2vk8yh7e\\temp_input.mp4', '-vf', 'scale=trunc((iw*1.15)/2)*2:trunc(ow/a/2)*2', '-pix_fmt', 'yuv420p', 'zoomed.mp4']
cpe.output b"ffmpeg version 7.1-essentials_build-www.gyan.dev Copyright (c) 2000-2024 the FFmpeg developers\r\n  built with gcc 14.2.0 (Rev1, Built by MSYS2 project)\r\n  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband\r\n  libavutil      59. 39.100 / 59. 39.100\r\n  libavcodec     61. 19.100 / 61. 19.100\r\n  libavformat    61.  7.100 / 61.  7.100\r\n  libavdevice    61.  3.100 / 61.  3.100\r\n  libavfilter    10.  4.100 / 10.  4.100\r\n  libswscale      8.  3.100 /  8.  3.100\r\n  libswresample   5.  3.100 /  5.  3.100\r\n  libpostproc    58.  3.100 / 58.  3.100\r\nInput #0, mov,mp4,m4a,3gp,3g2,mj2, from 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp2vk8yh7e\\temp_input.mp4':\r\n  Metadata:\r\n    major_brand     : isom\r\n    minor_version   : 512\r\n    compatible_brands: isomiso2mp41\r\n    encoder         : Lavf58.76.100\r\n  Duration: 00:00:02.47, start: 0.000000, bitrate: 47465 kb/s\r\n  Stream #0:0[0x1](und): Video: mpeg4 (Simple Profile) (mp4v / 0x7634706D), yuv420p, 2160x3840 [SAR 1:1 DAR 9:16], 47459 kb/s, 59.94 fps, 59.94 tbr, 11988 tbn (default)\r\n      Metadata:\r\n        handler_name    : VideoHandler\r\n        vendor_id       : [0][0][0][0]\r\nStream mapping:\r\n  Stream #0:0 -> #0:0 (mpeg4 (native) -> h264 (libx264))\r\nPress [q] to stop, [?] for help\r\n[libx264 @ 0000020d89c971c0] using SAR=1/1\r\n[libx264 @ 0000020d89c971c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2\r\nx264 [error]: malloc of size 58401536 failed\r\n[vost#0:0/libx264 @ 0000020d89f10400] Error while opening encoder - maybe incorrect parameters such as bit_rate, rate, width or height.\r\n[vf#0:0 @ 0000020d89c97840] Error sending frames to consumers: Generic error in an external library\r\n[vf#0:0 @ 0000020d89c97840] Task finished with error code: -542398533 (Generic error in an external library)\r\n[vf#0:0 @ 0000020d89c97840] Terminating thread with return code -542398533 (Generic error in an external library)\r\n[vost#0:0/libx264 @ 0000020d89f10400] Could not open encoder before EOF\r\n[vost#0:0/libx264 @ 0000020d89f10400] Task finished with error code: -22 (Invalid argument)\r\n[vost#0:0/libx264 @ 0000020d89f10400] Terminating thread with return code -22 (Invalid argument)\r\n[out#0/mp4 @ 0000020d89ca3e80] Nothing was written into output file, because at least one of its streams received no packets.\r\nframe=    0 fps=0.0 q=0.0 Lsize=       0KiB time=N/A bitrate=N/A speed=N/A    \r\nConversion failed!\r\n"
Error: __traceback__ must be a traceback or None

=> always save to disk before stabilization
=> default to yes to do you want to stabilize
=> write a script only for stabilization
