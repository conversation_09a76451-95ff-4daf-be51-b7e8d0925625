#!/usr/bin/env python3

import subprocess
import json
import sys
from helper import is_number
from helper import PicnicException
import argparse, os
import shutil

class VideoStabilisingException(PicnicException):
    pass

class VideoBrokenException(PicnicException):
    pass

class InvalidParameterException(PicnicException):
    pass

def get_video_metadata(video_path):
    """Get video metadata using ffprobe directly."""
    try:
        # Use ffprobe to get video metadata in JSON format
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_streams',
            '-select_streams', 'v',  # Only video streams
            video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        metadata = json.loads(result.stdout)
        return metadata.get('streams', [])
    except (subprocess.CalledProcessError, json.JSONDecodeError, FileNotFoundError) as e:
        raise VideoBrokenException(f"Could not read video metadata: {str(e)}")

class StabVid(object):

    def __init__(self,
                 ffmpeg_full_path=None,
                 video_scale_factor="1.15",
                 video_zoom_factor="-15",
                 max_video_length_seconds=240):
        self.max_video_length_seconds = max_video_length_seconds
        self.min_video_length_seconds = 1
        
        # Find ffmpeg in system path if not provided
        if ffmpeg_full_path is None:
            ffmpeg_full_path = shutil.which('ffmpeg')
            if ffmpeg_full_path is None:
                raise InvalidParameterException("ffmpeg not found in system PATH")
        
        # Validate parameters
        try:
            float(video_scale_factor)
            if float(video_scale_factor) <= 0:
                raise InvalidParameterException("Scale factor must be positive")
        except ValueError:
            raise InvalidParameterException("Scale factor must be a number")
            
        try:
            float(video_zoom_factor)
        except ValueError:
            raise InvalidParameterException("Zoom factor must be a number")
            
        self.ffmpeg_full_path = ffmpeg_full_path
        self.video_scale_factor = video_scale_factor
        self.video_zoom_factor = video_zoom_factor

    def __call__(self, input_path, output_path):
        return self.stab_file(input_path, output_path)

    # ####################### #
    # ## functions ########## #
    # ####################### #

    def stab_file(self, input_path, output_path):

        zoomed_file_name = "zoomed.mp4"
        video_streams = get_video_metadata(input_path)
        if len(video_streams) > 1:
            raise VideoBrokenException("Video may not contain multiple video streams")
        if len(video_streams) < 1:
            raise VideoBrokenException("No video streams found in file")

        could_check_dur_initially = self.check_vid_duration(input_path)

        try:
            # zoom by the size of the zoom in the stabilization, the total output file is bigger,
            # but no resolution is lost to the crop
            subprocess.check_output(
                [self.ffmpeg_full_path,
                 "-y",
                 "-i", input_path,
                 "-vf", "scale=trunc((iw*" + self.video_scale_factor + ")/2)*2:trunc(ow/a/2)*2",
                 "-pix_fmt", "yuv420p",  # workaround for https://github.com/georgmartius/vid.stab/issues/36
                 zoomed_file_name],
                stderr=subprocess.STDOUT)

            if not could_check_dur_initially:
                # sometimes metadata on original vids were broken,
                # so we need to re-check after fixing it during the first ffmpeg-pass
                self.check_vid_duration(zoomed_file_name)

            subprocess.check_output(
                [self.ffmpeg_full_path,
                 "-y",
                 "-i", zoomed_file_name,
                 "-vf", "vidstabdetect",
                 "-f", "null",
                 "-"],
                stderr=subprocess.STDOUT)

            subprocess.check_output(
                [self.ffmpeg_full_path,
                 "-y",
                 "-i", zoomed_file_name,
                 "-vf", "vidstabtransform=smoothing=20:crop=black:zoom=" + self.video_zoom_factor
                 + ":optzoom=0:interpol=linear,unsharp=5:5:0.8:3:3:0.4",
                 output_path],
                stderr=subprocess.STDOUT)
        except subprocess.CalledProcessError as cpe:
            print("cpe.returncode", cpe.returncode)
            print("cpe.cmd", cpe.cmd)
            print("cpe.output", cpe.output)

            raise VideoStabilisingException("ffmpeg could't compute file").with_traceback(cpe)

    def check_vid_duration(self, path):
        video_streams = get_video_metadata(path)
        if len(video_streams) > 0 and "duration" in video_streams[0] \
                and is_number(video_streams[0]["duration"]):
            duration = float(video_streams[0]["duration"])
            if duration > self.max_video_length_seconds:
                raise VideoBrokenException("Video too long. Video duration: " + str(duration)
                                           + ", Maximum duration: " + str(self.max_video_length_seconds) + ". ")
            elif duration < self.min_video_length_seconds:
                raise VideoBrokenException("Video too short. Video duration: " + str(duration)
                                           + ", Minimum duration: " + str(self.min_video_length_seconds) + ". ")
            else:
                return True
        return False

if __name__ == "__main__":
    def file_path(string):
        if os.path.isfile(string):
            return string
        else:
            raise NotADirectoryError(f"File not found: {string}")

    def positive_float(string):
        try:
            value = float(string)
            if value <= 0:
                raise argparse.ArgumentTypeError(f"{string} must be positive")
            return str(value)
        except ValueError:
            raise argparse.ArgumentTypeError(f"{string} must be a number")

    def float_str(string):
        try:
            float(string)
            return string
        except ValueError:
            raise argparse.ArgumentTypeError(f"{string} must be a number")

    parser = argparse.ArgumentParser(description='Stabilize a single video')
    parser.add_argument('file_path', type=file_path, help='path to video file')
    parser.add_argument('--max-duration', type=int, help='maximum video duration in seconds (default: no limit)', default=sys.maxsize)
    parser.add_argument('--scale', type=positive_float, help='video scale factor (must be positive, default: 1.15)', default="1.15")
    parser.add_argument('--zoom', type=float_str, help='video zoom factor (default: -15)', default="-15")
    args = parser.parse_args()
    
    try:
        stabilizer = StabVid(
            max_video_length_seconds=args.max_duration,
            video_scale_factor=args.scale,
            video_zoom_factor=args.zoom
        )
        print("input: " + str(args.file_path))
        print("stabilizing...")
        output = os.path.join(os.path.dirname(args.file_path), 'stabilized.mp4')
        stabilizer(args.file_path, output)
        print("done")
        print("output: " + str(output))
    except PicnicException as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}", file=sys.stderr)
        sys.exit(1)
